# send_email.py
from flask_mail import Mail, Message
from flask import current_app
from typing import List, Optional
from datetime import datetime, timedelta

# Initialize Mail object
mail = Mail()

# Default email settings
# Primary recipient
DEFAULT_RECIPIENTS = ["<EMAIL>", "<EMAIL>"]
# Add recipients to the CC list as needed.
DEFAULT_CC = ["****************", "<EMAIL>", "<EMAIL>"]


def get_tracking_link(tracking_number: str) -> str:
    """
    Generate tracking link based on the tracking number format.
    """
    tracking_number = tracking_number.strip()
    if tracking_number.startswith("1Z"):  # UPS
        return f"https://www.ups.com/track?tracknum={tracking_number}"
    elif tracking_number.startswith(("7", "8")):  # FedEx
        return f"https://www.fedex.com/fedextrack/?trknbr={tracking_number}"
    elif len(tracking_number) == 10 or tracking_number.startswith(("JD", "DHL")):  # DHL
        return f"https://www.dhl.com/track?tracking-number={tracking_number}"
    else:
        # Default to UPS as LIGENTEC currently uses UPS for shipping
        return f"https://www.ups.com/track?tracknum={tracking_number}"


def send_shipping_notification(
    tracking_number: str,
    ligentec_label_title: str,
    wafer_ids: List[str],
    manager_acronym: Optional[str] = None,
    contact_email: Optional[str] = None,
    additional_cc: Optional[List[str]] = None,
) -> None:
    """
    Sends shipment notification email.

    Args:
        tracking_number: Carrier tracking number
        ligentec_label_title: Title from Asana task
        wafer_ids: List of wafer IDs being shipped
        manager_acronym: Account/Project manager acronym
        contact_email: Contact person email
        additional_cc: Additional CC recipients
    """
    try:
        # Build CC list
        cc_list = list(DEFAULT_CC)

        # Add manager email if provided
        if manager_acronym:
            manager_email = f"{manager_acronym.lower()}@ligentec.com"
            if manager_email not in cc_list:
                cc_list.append(manager_email)

        # Add contact email if provided
        if contact_email and contact_email not in cc_list:
            cc_list.append(contact_email)

        # Add additional CC recipients
        if additional_cc:
            for email in additional_cc:
                if email not in cc_list:
                    cc_list.append(email)

        # Clean tracking number and get link
        clean_tracking = tracking_number.replace("Track", "").strip()
        tracking_link = get_tracking_link(clean_tracking)

        # Calculate delivery date
        delivery_date = datetime.now() + timedelta(days=3)
        formatted_date = f"{delivery_date.strftime('%B %d, %Y')}"

        # Format wafer list
        wafer_list = "\n".join(f"- {wafer_id}" for wafer_id in wafer_ids)

        # Create email content
        body = f"""
The above-mentioned order has been shipped.

Tracking Number: {clean_tracking}
Track your shipment: {tracking_link}

Please find enclosed the wafer IDs list:
{wafer_list}

With estimated delivery date {formatted_date}

Best regards,
Elisee Kajingu
Test Technician
Ligentec France
"""

        msg = Message(
            subject=f"Shipping Notification: {ligentec_label_title}",
            recipients=DEFAULT_RECIPIENTS,
            cc=cc_list,
            body=body,
            sender=current_app.config["MAIL_DEFAULT_SENDER"],
        )

        mail.send(msg)
        current_app.logger.info(
            (
                f"Shipping notification sent successfully with tracking number: "
                f"{clean_tracking}"
            )
        )

    except Exception as e:
        current_app.logger.error(f"Failed to send shipping notification: {str(e)}")
        raise


def send_test_email() -> None:
    """Sends a test email to verify configuration - only to specific recipients"""
    try:
        sender_email = current_app.config["MAIL_DEFAULT_SENDER"]

        # Filter the CC list to keep only elk and khk
        filtered_cc = ["<EMAIL>", "<EMAIL>"]

        msg = Message(
            subject="Test Email - Ligentec Labeller",
            recipients=[sender_email],  # Only send to the sender's email
            cc=filtered_cc,  # Only include elk and khk
            body=(
                "This is a test email to verify the email configuration is "
                "working correctly.\n\n"
                "This is a test email sent to you and selected Ligentec contacts."
            ),
            sender=sender_email,
        )

        mail.send(msg)
        current_app.logger.info(f"Test email sent successfully to {sender_email}")

    except Exception as e:
        current_app.logger.error(f"Failed to send test email: {str(e)}")
        raise
