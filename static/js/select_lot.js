/**
 * select_lot.js
 * Handles the logic for the Label & Packing Slip Generation interface
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize all functionality on page load
  initializeAll();
});

/**
 * Initialize all components and functionality
 */
function initializeAll() {
  initializeTabs();
  initializeFormSubmission();
  initializeTheme();
  initializeFormHandlers();
  initializeButtons();
  initializeAnimations();
}

/**
 * Initialize tab functionality with smooth transitions and indicator
 */
function initializeTabs() {
  const tabs = document.querySelectorAll(".tab-button");
  const contents = document.querySelectorAll(".tab-content");
  const indicator = document.querySelector(".tab-indicator");
  let activeTab = "asana"; // Default active tab

  // Set initial indicator position
  if (indicator) {
    const activeButton = document.querySelector(".tab-button.active");
    if (activeButton) {
      updateIndicator(activeButton);
    }
  }

  tabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      // Remove active state from all tabs
      tabs.forEach((t) => t.classList.remove("active"));

      // Add active state to clicked tab
      tab.classList.add("active");

      // Update indicator position
      if (indicator) {
        updateIndicator(tab);
      }

      // Update content visibility with animation
      const targetId = `${tab.dataset.tab}-tab`;
      contents.forEach((content) => {
        if (content.id === targetId) {
          content.classList.remove("hidden");
          // Trigger reflow for animation
          void content.offsetWidth;
          content.classList.add("active");
        } else {
          content.classList.remove("active");
          // Add hidden class after transition
          setTimeout(() => {
            if (!content.classList.contains("active")) {
              content.classList.add("hidden");
            }
          }, 300);
        }
      });

      activeTab = tab.dataset.tab;
      // Clear form fields when switching tabs
      clearFields();
    });
  });
}

/**
 * Updates the position and width of the tab indicator
 * @param {HTMLElement} tab - The active tab element
 */
function updateIndicator(tab) {
  const indicator = document.querySelector(".tab-indicator");
  if (!indicator) return;

  indicator.style.width = `${tab.offsetWidth}px`;
  indicator.style.left = `${tab.offsetLeft}px`;
}

/**
 * Initialize form submission with validation and loading states
 */
function initializeFormSubmission() {
  const form = document.querySelector("form");
  const lotSelect = document.querySelector('select[name="lot"]');
  const multipleLotsInput = document.querySelector(
    'textarea[name="multiple_lots"]'
  );
  const asanaLinkInput = document.getElementById("asana_link");

  // Field clearing logic
  lotSelect?.addEventListener("change", function () {
    if (this.value) {
      multipleLotsInput.value = "";
      asanaLinkInput.value = "";
    }
  });

  multipleLotsInput?.addEventListener("input", function () {
    if (this.value) {
      lotSelect.value = "";
      asanaLinkInput.value = "";
    }
  });

  asanaLinkInput?.addEventListener("input", function () {
    if (this.value) {
      lotSelect.value = "";
      multipleLotsInput.value = "";
    }
  });

  // Form submission with enhanced AJAX handling
  form?.addEventListener("submit", function (e) {
    console.log("Form submission triggered");
    e.preventDefault();

    // Get active tab
    const activeTabContent = document.querySelector(".tab-content.active");
    const activeTabId = activeTabContent?.id?.replace("-tab", "");

    // Validation
    let isValid = false;
    let errorMessage = "";

    if (activeTabId === "manual") {
      const lotValue = lotSelect?.value;
      const multipleLotsValue = multipleLotsInput?.value.trim();

      isValid = lotValue || multipleLotsValue;
      errorMessage = "Please select a lot or enter multiple lots";
    } else if (activeTabId === "asana") {
      isValid = asanaLinkInput?.value.trim();
      errorMessage = "Please enter an Asana task link";
    } else if (activeTabId === "search") {
      // Validate search criteria
      const searchLot = document.getElementById("search_lot")?.value.trim();
      const searchScribe = document
        .getElementById("search_scribe")
        ?.value.trim();
      isValid = searchLot || searchScribe;
      errorMessage = "Please enter at least one search criteria";
    } else if (activeTabId === "freestyle") {
      // Validate free style inputs
      const itemId = document.getElementById("fs_item_id")?.value.trim();
      const waferCount = document
        .getElementById("fs_wafer_count")
        ?.value.trim();
      const svmLotId = document.getElementById("fs_svm_lot_id")?.value.trim();

      isValid = itemId && waferCount && svmLotId;

      if (!itemId) {
        errorMessage = "Please enter an Item ID";
      } else if (!waferCount) {
        errorMessage = "Please enter a Wafer Count";
      } else if (!svmLotId) {
        errorMessage = "Please enter an SVM Lot ID";
      }
    }

    if (!isValid) {
      Swal.fire({
        icon: "error",
        title: "Selection Required",
        text: errorMessage,
        showClass: {
          popup: "animate__animated animate__fadeIn animate__faster",
        },
        hideClass: {
          popup: "animate__animated animate__fadeOut animate__faster",
        },
      });
      return;
    }

    // Show loading indicator with custom styling
    Swal.fire({
      title: "Processing Request",
      html: `
        <div class="flex flex-col items-center">
          <div class="loading-spinner mb-4"></div>
          <p>Please wait while we process your request...</p>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      customClass: {
        popup: "rounded-xl",
      },
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Special handling for Asana tab to use AJAX
    if (activeTabId === "asana") {
      submitAsanaForm(this, new FormData(this));
    } else if (activeTabId === "search") {
      // Handle Quick Search tab with AJAX
      submitQuickSearchForm(this, new FormData(this));
    } else if (activeTabId === "freestyle") {
      // Handle Free Style tab with AJAX
      submitFreeStyleForm(this, new FormData(this));
    } else {
      // Standard form submission for other tabs
      this.submit();
    }
  });
}

/**
 * Submit Free Style form via AJAX
 * @param {HTMLFormElement} form - The form element
 * @param {FormData} formData - The form data to submit
 */
async function submitFreeStyleForm(form, formData) {
  try {
    // Show loading indicator
    Swal.fire({
      title: "Processing...",
      html: `
        <div class="flex flex-col items-center">
          <div class="loading-spinner mb-4"></div>
          <p>Please wait while we generate your Substrate-wafer label...</p>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      customClass: {
        popup: "rounded-xl",
      },
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a flag to indicate this is a free style submission
    formData.append("is_freestyle", "true");

    // Send the free style request
    const baseUrl = form.action.split("/").slice(0, -1).join("/");
    const freestyleUrl = `${baseUrl}/freestyle`;
    console.log("Submitting free style to URL:", freestyleUrl);

    // Try to get CSRF token
    let csrfToken = "";
    try {
      csrfToken = getCsrfToken();
    } catch (e) {
      console.warn("CSRF token not found, continuing without it:", e);
    }

    const response = await fetch(freestyleUrl, {
      method: "POST",
      body: formData,
      headers: {
        "X-Requested-With": "XMLHttpRequest",
        "X-CSRFToken": csrfToken,
      },
    });

    // Check if JSON response
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();

      if (response.ok) {
        if (data.redirect) {
          // Update the loading message to indicate we're redirecting to download page
          Swal.update({
            html: `
              <div class="flex flex-col items-center">
                <div class="loading-spinner mb-4"></div>
                <p>Label generated successfully!</p>
                <p class="text-sm text-gray-500 mt-2">Redirecting to download page...</p>
              </div>
            `,
          });

          // Short delay to show the success message before redirecting
          setTimeout(() => {
            window.location.href = data.redirect;
          }, 1000);
        } else if (data.message) {
          Swal.fire({
            icon: "info",
            title: "Substrate-wafer Label",
            text: data.message,
            confirmButtonColor: "#4f46e5",
          });
        } else {
          throw new Error("No redirect URL or message provided in response");
        }
      } else {
        throw new Error(
          data.message || `HTTP error! status: ${response.status}`
        );
      }
    } else {
      // Handle HTML response (traditional redirect)
      if (response.ok) {
        window.location.href = response.url;
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }
  } catch (error) {
    console.error("Error:", error);
    Swal.fire({
      icon: "error",
      title: "Substrate-wafer Label Failed",
      text:
        error.message ||
        "An error occurred while processing your Substrate-wafer label. Please try again.",
      confirmButtonColor: "#4f46e5",
    });
  }
}

/**
 * Submit Quick Search form via AJAX
 * @param {HTMLFormElement} form - The form element
 * @param {FormData} formData - The form data to submit
 */
async function submitQuickSearchForm(form, formData) {
  try {
    // Show loading indicator
    Swal.fire({
      title: "Searching...",
      html: `
        <div class="flex flex-col items-center">
          <div class="loading-spinner mb-4"></div>
          <p>Please wait while we search for matching wafers...</p>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      customClass: {
        popup: "rounded-xl",
      },
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a flag to indicate this is a quick search
    formData.append("is_quick_search", "true");

    // Send the search request
    // Use the form's action URL as the base and append the quick_search endpoint
    const baseUrl = form.action.split("/").slice(0, -1).join("/");
    const searchUrl = `${baseUrl}/quick_search`;
    console.log("Submitting quick search to URL:", searchUrl);

    const response = await fetch(searchUrl, {
      method: "POST",
      body: formData,
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    // Check if JSON response
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();

      if (response.ok) {
        if (data.redirect) {
          window.location.href = data.redirect;
        } else if (data.message) {
          Swal.fire({
            icon: "info",
            title: "Search Results",
            text: data.message,
            confirmButtonColor: "#4f46e5",
          });
        } else {
          throw new Error("No redirect URL or message provided in response");
        }
      } else {
        throw new Error(
          data.message || `HTTP error! status: ${response.status}`
        );
      }
    } else {
      // Handle HTML response (traditional redirect)
      if (response.ok) {
        window.location.href = response.url;
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }
  } catch (error) {
    console.error("Error:", error);
    Swal.fire({
      icon: "error",
      title: "Search Failed",
      text:
        error.message ||
        "An error occurred while processing your search. Please try again.",
      confirmButtonColor: "#4f46e5",
    });
  }
}

/**
 * Submit Asana form via AJAX
 * @param {HTMLFormElement} form - The form element
 * @param {FormData} formData - The form data to submit
 */
async function submitAsanaForm(form, formData) {
  try {
    const response = await fetch(form.action, {
      method: "POST",
      body: formData,
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    // Check if JSON response
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();

      if (response.ok) {
        if (data.redirect) {
          window.location.href = data.redirect;
        } else {
          throw new Error("No redirect URL provided in response");
        }
      } else {
        throw new Error(
          data.message || `HTTP error! status: ${response.status}`
        );
      }
    } else {
      // Handle HTML response (traditional redirect)
      if (response.ok) {
        window.location.href = response.url;
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    }
  } catch (error) {
    console.error("Error:", error);
    Swal.fire({
      icon: "error",
      title: "Request Failed",
      text:
        error.message ||
        "An error occurred while processing your request. Please try again.",
      confirmButtonColor: "#4f46e5",
    });
  }
}

/**
 * Initialize theme toggling functionality
 */
function initializeTheme() {
  const themeToggle = document.getElementById("themeToggle");
  const html = document.documentElement;
  const savedTheme = localStorage.getItem("theme") || "light";

  function setTheme(theme) {
    if (theme === "dark") {
      html.classList.add("dark");
      document.querySelector(".fa-sun")?.classList.add("hidden");
      document.querySelector(".fa-moon")?.classList.remove("hidden");
    } else {
      html.classList.remove("dark");
      document.querySelector(".fa-sun")?.classList.remove("hidden");
      document.querySelector(".fa-moon")?.classList.add("hidden");
    }
    localStorage.setItem("theme", theme);
  }

  setTheme(savedTheme);

  if (themeToggle) {
    themeToggle.addEventListener("click", () => {
      const currentTheme = html.classList.contains("dark") ? "light" : "dark";
      setTheme(currentTheme);
    });
  }
}

/**
 * Initialize form input handlers and validations
 */
function initializeFormHandlers() {
  // Initialize date validation
  const dateFrom = document.getElementById("search_date_from");
  const dateTo = document.getElementById("search_date_to");

  if (dateFrom && dateTo) {
    dateFrom.addEventListener("change", validateDates);
    dateTo.addEventListener("change", validateDates);
  }

  // Remove required attribute from date inputs to prevent form validation issues
  document.querySelectorAll('input[type="date"]').forEach((input) => {
    input.removeAttribute("required");
  });
}

/**
 * Initialize action buttons
 */
function initializeButtons() {
  // Initialize Eiger CSV button
  const eigerCsvBtn = document.getElementById("eigerCsvBtn");
  if (eigerCsvBtn) {
    eigerCsvBtn.addEventListener("click", generateEigerCsv);
  }

  // Initialize email buttons
  const sendEmailBtn = document.getElementById("sendEmailBtn");
  if (sendEmailBtn) {
    sendEmailBtn.addEventListener("click", sendNotification);
  }

  const testEmailBtn = document.getElementById("testEmailBtn");
  if (testEmailBtn) {
    testEmailBtn.addEventListener("click", testEmail);
  }
}

/**
 * Initialize UI animations and interactions
 */
function initializeAnimations() {
  // Add entrance animations for page elements
  const header = document.querySelector(".container > div:first-child");
  const mainCard = document.querySelector(".card-hover");

  if (header && mainCard) {
    // Apply subtle entrance animations
    header.style.opacity = "0";
    header.style.transform = "translateY(-20px)";

    mainCard.style.opacity = "0";
    mainCard.style.transform = "translateY(20px)";

    // Trigger animations after a small delay
    setTimeout(() => {
      header.style.transition = "opacity 600ms ease, transform 600ms ease";
      header.style.opacity = "1";
      header.style.transform = "translateY(0)";

      setTimeout(() => {
        mainCard.style.transition = "opacity 600ms ease, transform 600ms ease";
        mainCard.style.opacity = "1";
        mainCard.style.transform = "translateY(0)";
      }, 200);
    }, 100);
  }
}

/**
 * Clear all form fields
 */
function clearFields() {
  const fields = [
    "lot",
    "multiple_lots",
    "asana_link",
    "search_lot",
    "search_scribe",
    "search_date_from",
    "search_date_to",
    "fs_item_id",
    "fs_wafer_count",
    "fs_svm_lot_id",
    "fs_comments",
  ];

  fields.forEach((id) => {
    const element = document.getElementById(id);
    if (element) {
      element.value = "";
    }
  });
}

/**
 * Validate date range inputs
 */
function validateDates() {
  const dateFrom = document.getElementById("search_date_from");
  const dateTo = document.getElementById("search_date_to");

  if (dateFrom && dateTo && dateFrom.value && dateTo.value) {
    if (dateFrom.value > dateTo.value) {
      dateTo.setCustomValidity("End date must be after start date");
      dateTo.reportValidity();

      // Visual feedback
      dateTo.classList.add("border-red-500");

      // Remove error styling after correction
      dateTo.addEventListener("input", function removeError() {
        if (dateFrom.value <= dateTo.value) {
          dateTo.classList.remove("border-red-500");
          dateTo.setCustomValidity("");
          dateTo.removeEventListener("input", removeError);
        }
      });
    } else {
      dateTo.setCustomValidity("");
      dateTo.classList.remove("border-red-500");
    }
  }
}

/**
 * Get CSRF token from the page
 * @returns {string} CSRF token
 */
function getCsrfToken() {
  const token =
    document.querySelector('meta[name="csrf-token"]')?.content ||
    document.querySelector('input[name="csrf_token"]')?.value ||
    document.querySelector('[name="csrf_token"]')?.value;

  if (!token) {
    throw new Error("CSRF token not found");
  }
  return token;
}

/**
 * Generate Eiger CSV file
 */
async function generateEigerCsv() {
  try {
    const csrfToken = getCsrfToken();

    const { value: asanaLink, isConfirmed } = await Swal.fire({
      title: "Generate Eiger CSV",
      html: `
        <div class="text-left">
          <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link</label>
          <input id="swal-input-asana" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here">
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: "Generate",
      confirmButtonColor: "#10b981",
      cancelButtonColor: "#6b7280",
      preConfirm: () => {
        const input = document.getElementById("swal-input-asana");
        if (!input.value.trim()) {
          Swal.showValidationMessage("Please enter an Asana task link");
          return false;
        }
        return input.value;
      },
      customClass: {
        popup: "rounded-lg",
      },
    });

    if (!isConfirmed || !asanaLink) return;

    Swal.fire({
      title: "Generating CSV",
      html: `
        <div class="flex flex-col items-center">
          <div class="csv-loading-spinner mb-4"></div>
          <div class="text-sm text-gray-500">Fetching task information and generating CSV file...</div>
        </div>
      `,
      customClass: {
        popup: "rounded-lg",
      },
      showConfirmButton: false,
      allowOutsideClick: false,
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .csv-loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #d1fae5;
            border-top: 5px solid #10b981;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a small delay to show the loading animation
    await new Promise((resolve) => setTimeout(resolve, 800));

    // First, get the task info to extract the task name
    // Extract the task GID from various Asana URL formats
    const taskGidMatch =
      asanaLink.match(/\/task\/(\d+)/) ||
      asanaLink.match(/\/(\d+)\?/) ||
      asanaLink.match(/\/(\d+)$/) ||
      asanaLink.match(/\/project\/\d+\/task\/(\d+)/);
    const taskGid = taskGidMatch ? taskGidMatch[1] : null;

    if (!taskGid) {
      throw new Error("Invalid Asana task link format");
    }

    // Fetch task info to get the task name
    const taskInfoResponse = await fetch(
      `/api/get_task_info?task_gid=${taskGid}`
    );
    let taskName = "";

    if (taskInfoResponse.ok) {
      const taskInfoData = await taskInfoResponse.json();
      if (
        taskInfoData.success &&
        taskInfoData.task_info &&
        taskInfoData.task_info.name
      ) {
        // Get the original task name
        const originalName = taskInfoData.task_info.name;

        // Extract project info using similar logic to the backend
        let projectInfo = "";

        // Try to extract relevant project information and exclude lot IDs and "label free" mentions
        // Look for patterns like "FAU WP1.9 RI_Oxide" or parts after commas
        const projectMatch = originalName.match(
          /([A-Z]+\s*W?P?\.?\d+\.?\d*\s*[A-Z][A-Za-z0-9_]+(?:\s*[-]\s*\d+\s*[A-Z]+\s*)*)/
        );

        if (projectMatch) {
          projectInfo = projectMatch[1].trim();
        } else {
          // Try to find any part after a comma that might contain the project info
          const commaMatch = originalName.match(/,\s*([^,]+)(?:,|$)/);
          if (commaMatch) {
            projectInfo = commaMatch[1].trim();
          } else {
            // Just use the name but try to remove "Eiger" and "Label free" parts
            projectInfo = originalName
              .replace(/Eiger\s*Label\s*free\s*:\s*[^,]+,/i, "")
              .replace(/^Eiger\s*/i, "")
              .trim();
          }
        }

        // Clean the project info for use in a filename
        taskName = projectInfo
          .replace(/[^a-zA-Z0-9]/g, "_") // Replace special chars with underscore
          .replace(/_+/g, "_") // Replace multiple underscores with a single one
          .replace(/^_|_$/g, "") // Remove leading/trailing underscores
          .substring(0, 50); // Limit length to avoid very long filenames
      }
    }

    const response = await fetch("/generate_eiger_csv", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": csrfToken,
      },
      body: JSON.stringify({
        asana_link: asanaLink,
        direct_generation: true,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Failed to generate CSV");
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;

    // Get the filename from the Content-Disposition header if available
    let serverFilename = "";

    // Extract the filename from the Content-Disposition header
    const contentDisposition = response.headers.get("Content-Disposition");
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch && filenameMatch[1]) {
        serverFilename = filenameMatch[1];
        console.log("Using server-provided filename:", serverFilename);
      }
    }

    // If we got a server filename, use it
    if (serverFilename) {
      a.download = serverFilename;
    } else {
      // Fall back to client-side filename generation
      console.log("No server filename found, generating locally");

      // Create a timestamp with date and time (YYYY-MM-DD_HH-MM-SS)
      const now = new Date();
      const dateStr = now.toISOString().slice(0, 10);
      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, "-");
      const timestamp = `${dateStr}_${timeStr}`;

      // Check if taskName already contains "Eiger" to avoid duplication
      if (taskName.toLowerCase().startsWith("eiger_")) {
        taskName = taskName.substring(6); // Remove "Eiger_" prefix
      } else if (taskName.toLowerCase().startsWith("eiger")) {
        taskName = taskName.substring(5); // Remove "Eiger" prefix
      }

      // Always prepend "Eiger_" to the filename
      a.download = taskName
        ? `Eiger_${taskName}_${timestamp}.csv`
        : `Eiger_${timestamp}.csv`;
    }

    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    await Swal.fire({
      icon: "success",
      title: "Success",
      text: "CSV file has been generated and downloaded.",
      confirmButtonText: "Done",
      confirmButtonColor: "#10b981",
      customClass: {
        popup: "rounded-lg",
      },
    });
  } catch (error) {
    console.error("CSV generation error:", error);
    await Swal.fire({
      icon: "error",
      title: "Error",
      text:
        error.message ||
        "Failed to generate CSV file. Please check the Asana link and try again.",
      confirmButtonText: "OK",
      confirmButtonColor: "#4f46e5",
      customClass: {
        popup: "rounded-lg",
      },
    });
  }
}
/**
 * Send notification email
 */
async function sendNotification() {
  try {
    const csrfToken = getCsrfToken();

    // Prompt for Asana task link
    const { value: asanaLink, isConfirmed } = await Swal.fire({
      title: "Send Email Notification",
      html: `
        <div class="text-left">
          <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link</label>
          <input id="swal-input-asana-email" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here">
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: "Send Email",
      confirmButtonColor: "#3b82f6",
      cancelButtonColor: "#6b7280",
      preConfirm: () => {
        const input = document.getElementById("swal-input-asana-email");
        if (!input.value.trim()) {
          Swal.showValidationMessage("Please enter an Asana task link");
          return false;
        }
        return input.value;
      },
      customClass: {
        popup: "rounded-lg",
      },
    });

    if (!isConfirmed || !asanaLink) return;

    // Extract the task GID from various Asana URL formats
    const taskGidMatch =
      asanaLink.match(/\/task\/(\d+)/) ||
      asanaLink.match(/\/(\d+)\?/) ||
      asanaLink.match(/\/(\d+)$/) ||
      asanaLink.match(/\/project\/\d+\/task\/(\d+)/);
    const taskGid = taskGidMatch ? taskGidMatch[1] : null;

    if (!taskGid) {
      throw new Error("Invalid Asana task link. Please provide a valid link.");
    }

    // First, check if this is an Eiger project by making a preliminary call
    const checkResponse = await fetch(`/api/get_task_info?task_gid=${taskGid}`);
    const taskData = await checkResponse.json();

    if (!taskData.success) {
      throw new Error(taskData.message || "Failed to get task information");
    }

    // Check if it's an Eiger project by looking at Lot project field
    const lotProject = taskData.task_info["Lot project"] || "";
    const isEigerProject = lotProject.toLowerCase().includes("eiger");

    // If not an Eiger project, show a confirmation dialog first
    if (!isEigerProject) {
      const { isConfirmed } = await Swal.fire({
        icon: "warning",
        title: "Non-Eiger Shipment",
        html: `
          <div class="text-left">
            <p class="font-semibold mb-2">This does not appear to be an Eiger shipment.</p>
            <p class="mb-4">The Lot project field does not contain 'Eiger'.</p>
            <p>Do you want to send the email notification anyway?</p>
          </div>
        `,
        showCancelButton: true,
        confirmButtonText: "Yes, Send Anyway",
        cancelButtonText: "Cancel",
        confirmButtonColor: "#f59e0b",
        cancelButtonColor: "#6b7280",
        customClass: {
          popup: "rounded-lg",
        },
      });

      if (!isConfirmed) {
        return; // User canceled the operation
      }
    }

    Swal.fire({
      title: "Sending Notification",
      html: `
        <div class="flex flex-col items-center">
          <div class="email-loading-spinner mb-4"></div>
          <div class="text-sm text-gray-500">Preparing and sending email notification...</div>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      allowEscapeKey: false,
      customClass: {
        popup: "rounded-lg",
      },
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .email-loading-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #dbeafe;
            border-top: 5px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a small delay to show the loading animation
    await new Promise((resolve) => setTimeout(resolve, 800));

    const response = await fetch("/api/send_notification", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": csrfToken,
      },
      body: JSON.stringify({
        task_gid: taskGid,
        type: "shipping",
      }),
    });

    const data = await response.json();

    if (data.success) {
      // Check if it's not an Eiger project but sent anyway
      if (data.warning && !data.is_eiger) {
        await Swal.fire({
          icon: "warning",
          title: "Non-Eiger Shipment",
          html: `
            <div class="text-left">
              <p class="mb-4">Warning: ${data.warning}</p>
              <p>The email notification was sent successfully, but please verify this was intentional.</p>
            </div>
          `,
          confirmButtonColor: "#f59e0b",
          customClass: {
            popup: "rounded-lg",
          },
        });
      } else {
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Email notification sent successfully!",
          confirmButtonColor: "#3b82f6",
          customClass: {
            popup: "rounded-lg",
          },
        });
      }
    } else if (data.message && data.message.includes("tracking number")) {
      await Swal.fire({
        icon: "warning",
        title: "Tracking Number Required",
        text: "Please ensure the UPS Tracking Number is filled in Asana before sending the notification.",
        confirmButtonColor: "#f59e0b",
        customClass: {
          popup: "rounded-lg",
        },
      });
    } else {
      throw new Error(data.message || "Failed to send notification");
    }
  } catch (error) {
    console.error("Email error:", error);
    await Swal.fire({
      icon: "error",
      title: "Error",
      html: `Failed to send notification:<br>${error.message}`,
      confirmButtonColor: "#ef4444",
      customClass: {
        popup: "rounded-lg",
      },
    });
  }
}

/**
 * Get task GID from various sources
 * @returns {string} Asana task GID
 */
async function getTaskGid() {
  const taskGid =
    document.querySelector('[name="task_gid"]')?.value ||
    document.querySelector("#asana_link")?.value?.match(/\d+$/)?.[0] ||
    (typeof sessionStorage !== "undefined" &&
      sessionStorage.getItem("current_task_gid"));

  if (!taskGid) {
    throw new Error(
      "No task selected. Please select a task or enter an Asana link first."
    );
  }
  return taskGid;
}

/**
 * Send test email
 */
async function testEmail() {
  try {
    const csrfToken = getCsrfToken();

    // Prompt for Asana task link to test Eiger verification
    const { value: asanaLink, isConfirmed } = await Swal.fire({
      title: "Send Test Email",
      html: `
        <div class="text-left">
          <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link (Optional)</label>
          <input id="swal-input-asana-test" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here to test Eiger verification">
          <p class="mt-2 text-xs text-gray-500">If provided, the system will verify if this is an Eiger shipment before sending the test email.</p>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: "Send Test Email",
      confirmButtonColor: "#3b82f6",
      cancelButtonColor: "#6b7280",
      customClass: {
        popup: "rounded-lg",
      },
    });

    if (!isConfirmed) return;

    // Extract task GID if Asana link was provided
    let taskGid = null;
    if (asanaLink && typeof asanaLink === "string") {
      // Ensure asanaLink is a string before applying regex methods
      const taskGidMatch =
        asanaLink.match(/\/task\/(\d+)/) ||
        asanaLink.match(/\/(\d+)\?/) ||
        asanaLink.match(/\/(\d+)$/) ||
        asanaLink.match(/\/project\/\d+\/task\/(\d+)/);
      taskGid = taskGidMatch ? taskGidMatch[1] : null;

      // Check if asanaLink is just a number (direct GID input)
      if (!taskGid && /^\d+$/.test(asanaLink.trim())) {
        taskGid = asanaLink.trim();
      }
    }

    // If task GID provided, check if it's an Eiger project
    if (taskGid) {
      const checkResponse = await fetch(
        `/api/get_task_info?task_gid=${taskGid}`
      );
      const taskData = await checkResponse.json();

      if (taskData.success) {
        // Check if it's an Eiger project by looking at Lot project field
        const lotProject = taskData.task_info["Lot project"] || "";
        const isEigerProject = lotProject.toLowerCase().includes("eiger");

        // If not an Eiger project, show a confirmation dialog first
        if (!isEigerProject) {
          const { isConfirmed: proceedAnyway } = await Swal.fire({
            icon: "warning",
            title: "Non-Eiger Shipment",
            html: `
              <div class="text-left">
                <p class="font-semibold mb-2">This does not appear to be an Eiger shipment.</p>
                <p class="mb-4">The Lot project field does not contain 'Eiger'.</p>
                <p>Do you want to send the test email anyway?</p>
                <p class="mt-4 text-xs text-gray-500">Note: Test emails are only sent to admin recipients.</p>
              </div>
            `,
            showCancelButton: true,
            confirmButtonText: "Yes, Send Anyway",
            cancelButtonText: "Cancel",
            confirmButtonColor: "#f59e0b",
            cancelButtonColor: "#6b7280",
            customClass: {
              popup: "rounded-lg",
            },
          });

          if (!proceedAnyway) {
            return; // User canceled the operation
          }
        }
      }
    }

    Swal.fire({
      title: "Sending Test Email",
      html: `
        <div class="flex flex-col items-center">
          <div class="test-email-spinner mb-4"></div>
          <div class="text-sm text-gray-500">Sending test email to verify email functionality...</div>
        </div>
      `,
      showConfirmButton: false,
      allowOutsideClick: false,
      allowEscapeKey: false,
      customClass: {
        popup: "rounded-lg",
      },
      didOpen: () => {
        const style = document.createElement("style");
        style.textContent = `
          .test-email-spinner {
            width: 48px;
            height: 48px;
            border: 5px solid #dbeafe;
            border-top: 5px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `;
        Swal.getPopup().appendChild(style);
      },
    });

    // Add a small delay to show the loading animation
    await new Promise((resolve) => setTimeout(resolve, 800));

    const response = await fetch("/api/test_email", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": csrfToken,
      },
      body: JSON.stringify({
        task_gid: taskGid,
      }),
    });

    const data = await response.json();

    if (data.success) {
      // Check if it was a non-Eiger project but sent anyway
      if (data.warning && !data.is_eiger) {
        await Swal.fire({
          icon: "warning",
          title: "Non-Eiger Test Email Sent",
          html: `
            <div class="text-left">
              <p class="mb-4">Warning: ${data.warning}</p>
              <p>The test email was sent successfully to admin recipients only.</p>
              <p class="mt-4 text-xs text-gray-500">Remember: This was just a test and only went to admin emails.</p>
            </div>
          `,
          confirmButtonColor: "#f59e0b",
          customClass: {
            popup: "rounded-lg",
          },
        });
      } else {
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Test email sent successfully!",
          confirmButtonColor: "#3b82f6",
          customClass: {
            popup: "rounded-lg",
          },
        });
      }
    } else {
      throw new Error(data.message || "Failed to send test email");
    }
  } catch (error) {
    console.error("Test email error:", error);
    await Swal.fire({
      icon: "error",
      title: "Error",
      text: error.message || "Failed to send test email",
      confirmButtonColor: "#ef4444",
      customClass: {
        popup: "rounded-lg",
      },
    });
  }
}
