/* Global theme variables */
:root {
  --bg-primary-light: #ffffff;
  --bg-secondary-light: #f8f9fa;
  --text-primary-light: #212529;
  --text-secondary-light: #495057;
  --border-light: #dee2e6;
  --input-bg-light: #f8f9fa;
  --input-border-light: #ced4da;
  --btn-primary-light: #007bff;
  --btn-secondary-light: #6c757d;
  --btn-danger-light: #dc3545;
  --table-border-light: #dee2e6;
  --modal-bg-light: #ffffff;
  
  /* Dark theme */
  --bg-primary-dark: #1a1a1a;
  --bg-secondary-dark: #2d2d2d;
  --text-primary-dark: #f8f9fa;
  --text-secondary-dark: #adb5bd;
  --border-dark: #495057;
  --input-bg-dark: #343a40;
  --input-border-dark: #495057;
  --btn-primary-dark: #0d6efd;
  --btn-secondary-dark: #5c636a;
  --btn-danger-dark: #dc3545;
  --table-border-dark: #495057;
  --modal-bg-dark: #343a40;
}

/* Form groups */
.form-group {
  margin-bottom: 20px;
}

/* Form inputs */
input, select, textarea {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid var(--input-border-light);
  font-size: 16px;
  background-color: var(--input-bg-light);
  color: var(--text-primary-light);
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

textarea[readonly], input[readonly], select[disabled] {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.form-label {
  font-size: 1.1rem;
  color: var(--text-primary-light);
  transition: color 0.3s;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  margin-top: 15px;
  transition: background-color 0.3s, border-color 0.3s;
}

.btn-primary {
  background-color: var(--btn-primary-light);
  border-color: var(--btn-primary-light);
  color: white;
}

.btn-secondary {
  background-color: var(--btn-secondary-light);
  border-color: var(--btn-secondary-light);
  color: white;
}

.btn-danger {
  background-color: var(--btn-danger-light);
  border-color: var(--btn-danger-light);
  color: white;
}

/* Tables */
.table {
  color: var(--text-primary-light);
  transition: color 0.3s;
}

.table-bordered {
  border: 1px solid var(--table-border-light);
}

.table-bordered th,
.table-bordered td {
  padding: 15px;
  text-align: left;
  vertical-align: middle;
  border-color: var(--table-border-light);
}

.table-responsive {
  margin-top: 20px;
}

/* Modals */
.modal-title {
  font-weight: bold;
  color: var(--text-primary-light);
}

.modal-content {
  padding: 20px;
  background-color: var(--modal-bg-light);
  transition: background-color 0.3s;
}

/* Preview */
#previewIframe {
  border-radius: 5px;
  border: 1px solid var(--border-light);
}

.iframe-style {
  height: 500px;
}

/* Logo */
.logo {
  height: auto;
  max-height: 80px;
  width: auto;
  max-width: 150px;
}

/* Dark mode styles */
.dark input, .dark select, .dark textarea {
  background-color: var(--input-bg-dark);
  border-color: var(--input-border-dark);
  color: var(--text-primary-dark);
}

.dark .form-label {
  color: var(--text-primary-dark);
}

.dark .btn-primary {
  background-color: var(--btn-primary-dark);
  border-color: var(--btn-primary-dark);
}

.dark .btn-secondary {
  background-color: var(--btn-secondary-dark);
  border-color: var(--btn-secondary-dark);
}

.dark .btn-danger {
  background-color: var(--btn-danger-dark);
  border-color: var(--btn-danger-dark);
}

.dark .table {
  color: var(--text-primary-dark);
}

.dark .table-bordered {
  border-color: var(--table-border-dark);
}

.dark .table-bordered th,
.dark .table-bordered td {
  border-color: var(--table-border-dark);
}

.dark .modal-title {
  color: var(--text-primary-dark);
}

.dark .modal-content {
  background-color: var(--modal-bg-dark);
}

.dark #previewIframe {
  border-color: var(--border-dark);
}

/* Transition all elements */
.dark-transition * {
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}
