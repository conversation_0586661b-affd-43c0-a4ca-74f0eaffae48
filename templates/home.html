{% extends "base.html" %} {% block title %}Talaria - Home{% endblock %} {% block
head %} {{ super() }}
<style>
  /* Pulse animation for the status dot */
  @keyframes pulse {
    0% {
      opacity: 0.6;
      transform: scale(1);
    }

    50% {
      opacity: 1;
      transform: scale(1.2);
    }

    100% {
      opacity: 0.6;
      transform: scale(1);
    }
  }

  .pulse-dot {
    animation: pulse 2s infinite;
  }

  /* Tooltip styles */
  [title] {
    position: relative;
    cursor: help;
  }

  /* Enhance glass card hover effect */
  .glass-card {
    transition: all 0.3s ease;
  }

  .glass-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
</style>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-6">
  <!-- Enhanced Header Section - Mobile Responsive -->
  <div class="welcome-section mb-6 md:mb-10 rounded-xl overflow-hidden">
    <div class="welcome-content p-4 md:p-6">
      <h1
        class="typewriter text-3xl md:text-5xl font-bold text-white mb-2 md:mb-3 break-words"
      >
        Welcome to Talaria Dashboard
      </h1>
      <p class="typewriter-subtitle text-lg md:text-xl text-white">
        Shipment, wafer inventory management and logistics
      </p>

      <!-- Quick summary stats - mobile responsive with tooltips -->
      <div class="welcome-stats flex flex-wrap gap-3 md:gap-6 mt-4 md:mt-6">
        <div
          class="glass-card flex-1 min-w-[120px]"
          title="Total number of shipment tasks in Asana"
        >
          <p class="text-white text-xs md:text-sm font-medium">
            Total Shipments
          </p>
          <p class="text-white text-xl md:text-2xl font-bold">
            <span data-stat="total-shipments"
              >{{ asana_stats.total_shipments|default('--') }}</span
            >
            <span class="text-xs text-white-300 opacity-70">tasks</span>
          </p>
        </div>
        <div
          class="glass-card flex-1 min-w-[120px]"
          title="Percentage of active shipments that are on schedule"
        >
          <p class="text-white text-xs md:text-sm font-medium">On-time Rate</p>
          <p class="text-white text-xl md:text-2xl font-bold">
            <span
              data-stat="on-time-rate"
              class="{% if asana_stats.on_time_rate|default(0) < 50 %}text-red-300{% elif asana_stats.on_time_rate|default(0) < 80 %}text-yellow-300{% else %}text-green-300{% endif %}"
            >
              {{ asana_stats.on_time_rate|default('--') }}
            </span>
            <span class="text-white">%</span>
          </p>
        </div>
        <div
          class="glass-card pulse flex-1 min-w-[120px]"
          title="Number of shipments currently in progress"
        >
          <p class="text-white text-xs md:text-sm font-medium">
            Active Shipments
          </p>
          <p class="text-white text-xl md:text-2xl font-bold">
            <span data-stat="active-shipments"
              >{{ asana_stats.active_shipments|default('--') }}</span
            >
            <span class="text-xs text-white-300 opacity-70">in progress</span>
          </p>
        </div>
      </div>

      <!-- Status indicator for data freshness with debug link -->
      <div class="mt-3 text-right">
        <span class="text-xs text-white opacity-70">
          <span
            class="inline-block h-2 w-2 rounded-full {% if asana_stats %}bg-green-400 pulse-dot{% else %}bg-red-400{% endif %} mr-1"
          ></span>
          Data {% if asana_stats %}live{% else %}unavailable{% endif %} {% if
          current_user and current_user.is_admin %}
          <a
            href="/api/dashboard/debug"
            target="_blank"
            class="ml-2 text-blue-300 hover:text-blue-200 underline"
            >Debug</a
          >
          {% endif %}
        </span>
      </div>
    </div>
  </div>

  <!-- Main Dashboard Content -->
  <div
    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12 gap-4 table-responsive"
  >
    <!-- Key Metrics Row -->
    <div class="sm:col-span-1 lg:col-span-4">
      <div class="dashboard-card">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Available Lots
        </h3>
        <div class="flex items-center justify-between">
          <div>
            <h2
              class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white"
              id="availableLots"
            >
              <span data-stat="available-lots">{{ available_lots }}</span>
              <span class="text-lg text-gray-500 dark:text-gray-400"
                >/{{ total_lots }}</span
              >
            </h2>
            <div class="progress-bar mt-3">
              <div
                class="progress-bar-fill gradient-blue capacity-{{ capacity_percentage }}"
              ></div>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {{ capacity_percentage }}% of total capacity
            </p>
          </div>
          <div class="metric-icon gradient-blue">
            <i class="fas fa-layer-group text-2xl"></i>
          </div>
        </div>
        <div class="mt-4">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {% if lot_change > 0 %}
            <span class="text-green-500"
              ><i class="fas fa-arrow-up"></i> {{ lot_change }}%</span
            >
            from last month {% elif lot_change < 0 %}
            <span class="text-red-500"
              ><i class="fas fa-arrow-down"></i> {{ lot_change|abs }}%</span
            >
            from last month {% else %}
            <span class="text-gray-500">No change</span> from last month {%
            endif %}
          </p>
        </div>
      </div>
    </div>

    <div class="sm:col-span-1 lg:col-span-4">
      <div class="dashboard-card">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Available Wafers
        </h3>
        <div class="flex items-center justify-between">
          <div>
            <h2
              class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white"
              id="availableWafers"
            >
              <span data-stat="available-wafers">{{ available_wafers }}</span>
            </h2>
            <div class="progress-bar mt-3">
              <div
                class="progress-bar-fill gradient-green capacity-{{ wafer_percentage }}"
              ></div>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {{ wafer_percentage }}% of monthly target
            </p>
          </div>
          <div class="metric-icon gradient-green">
            <i class="fas fa-microchip text-2xl"></i>
          </div>
        </div>
        <div class="mt-4">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            <span class="text-green-500"
              ><i class="fas fa-arrow-up"></i> 8%</span
            >
            from last month
          </p>
        </div>
      </div>
    </div>

    <div class="sm:col-span-1 lg:col-span-4">
      <div class="dashboard-card">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Shipped Wafers
        </h3>
        <div class="flex items-center justify-between">
          <div>
            <h2
              class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white"
              id="shippedWafers"
            >
              <span data-stat="shipped-wafers">{{ shipped_wafers }}</span>
            </h2>
            <div class="progress-bar mt-3">
              <div
                class="progress-bar-fill gradient-amber capacity-{{ quarterly_progress }}"
              ></div>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
              {{ quarterly_progress }}% of quarterly goal ({{ quarterly_target
              }})
            </p>
          </div>
          <div class="metric-icon gradient-amber">
            <i class="fas fa-shipping-fast text-2xl"></i>
          </div>
        </div>
        <div class="mt-4">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {% if monthly_change > 0 %}
            <span class="text-green-500"
              ><i class="fas fa-arrow-up"></i> {{ monthly_change }}%</span
            >
            from last month {% elif monthly_change < 0 %}
            <span class="text-red-500"
              ><i class="fas fa-arrow-down"></i> {{ monthly_change|abs }}%</span
            >
            from last month {% else %}
            <span class="text-gray-500">No change</span> from last month {%
            endif %}
          </p>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="sm:col-span-2 lg:col-span-8">
      <div class="dashboard-card">
        <div
          class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6"
        >
          <h3
            class="text-lg font-semibold text-gray-900 dark:text-white mb-2 sm:mb-0"
          >
            Monthly Deliveries
          </h3>
          <div class="flex space-x-2 touch-target">
            <button
              type="button"
              class="px-3 py-1 text-sm bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 rounded-md touch-target"
            >
              This Year
            </button>
            <button
              type="button"
              class="px-3 py-1 text-sm bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-md touch-target"
            >
              Last Year
            </button>
          </div>
        </div>
        <div class="chart-height">
          <canvas id="monthlyChart"></canvas>
        </div>
      </div>
    </div>

    <div class="sm:col-span-2 lg:col-span-4">
      <div class="dashboard-card">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            Section Distribution
          </h3>
          <button
            type="button"
            id="refresh-chart"
            class="text-blue-600 dark:text-blue-400 text-sm p-2 touch-target"
          >
            <i class="fas fa-sync-alt mr-1"></i> Refresh
          </button>
        </div>
        <div class="chart-height">
          <canvas id="sectionChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Footer Section - Simplified -->
    <div class="col-span-12">
      <div class="dashboard-footer">
        <div class="footer-grid">
          <!-- Company Information - visible on all devices -->
          <div class="footer-section">
            <div class="footer-logo-container">
              <div class="logo-iridescent">
                <img
                  src="{{ url_for('static', filename='img/logoTalaria.jpeg') }}"
                  alt="Logo"
                  class="footer-logo img-fluid"
                />
              </div>
              <h3 class="footer-title">Talaria Dashboard</h3>
            </div>
            <p class="footer-description">
              Real-time shipment monitoring and logistics management system.
            </p>
          </div>

          <!-- Quick Links -->
          <div class="footer-section">
            <h4 class="footer-heading">Quick Links</h4>
            <ul class="footer-links">
              <li>
                <a href="{{ url_for('home') }}" class="footer-link touch-target"
                  ><i class="fas fa-chart-line mr-2"></i>Dashboard</a
                >
              </li>
              <li>
                <a
                  href="{{ url_for('shipment.shipment_dashboard') }}"
                  class="footer-link touch-target"
                  ><i class="fas fa-shipping-fast mr-2"></i>Shipments</a
                >
              </li>
              <li>
                <a
                  href="{{ url_for('inventory_management') }}"
                  class="footer-link touch-target"
                  ><i class="fas fa-boxes mr-2"></i>Inventory</a
                >
              </li>
              {% if check_permission('modify') %}
              <li>
                <a
                  href="{{ url_for('rfq.rfq_automation') }}"
                  class="footer-link touch-target"
                  ><i class="fas fa-envelope-open-text mr-2"></i>RFQ
                  Automation</a
                >
              </li>
              {% endif %}
            </ul>
          </div>

          <!-- Contact Information -->
          <div class="footer-section">
            <h4 class="footer-heading">Contact For Support</h4>
            <ul class="footer-contact">
              <li><i class="fas fa-envelope mr-2"></i><EMAIL></li>
              <li><i class="fas fa-phone-alt mr-2"></i>+33 7 76 02 67 88</li>
            </ul>
          </div>

          <!-- Stats Summary - hidden on smaller screens -->
          <div class="footer-section d-none d-lg-block">
            <h4 class="footer-heading">System Status</h4>
            <div class="footer-stats">
              <div class="footer-stat-item">
                <div class="stat-icon gradient-blue">
                  <i class="fas fa-server"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-label">Uptime</span>
                  <span class="stat-value">99.8%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Copyright and Social Media -->
        <div
          class="footer-bottom flex flex-col sm:flex-row justify-between items-center"
        >
          <div class="copyright mb-4 sm:mb-0 text-center sm:text-left">
            &copy; {{ datetime.now().year }} Talaria Dashboard
          </div>
          <div class="social-links">
            <a href="#" class="social-link touch-target" title="LinkedIn"
              ><i class="fab fa-linkedin"></i
            ></a>
            <a href="#" class="social-link touch-target" title="GitHub"
              ><i class="fab fa-github"></i
            ></a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script src="{{ url_for('static', filename='js/skeleton-loader.js') }}"></script>
<script src="{{ url_for('static', filename='js/home.js') }}"></script>
{% endblock %}
