{% extends "base.html" %}

{% block content %}
<div class="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-violet-50">
    <div class="bg-white p-8 rounded-2xl shadow-xl w-full max-w-md">
        <!-- IMPORTANT: Use this exact logo structure -->
        <div class="text-center mb-8">
            <div class="login-logo-container">
                <div class="login-logo-pulse"></div>
                <div class="login-logo-image">
                    <img src="{{ url_for('static', filename='img/logoTalaria.jpeg') }}" alt="Talaria Logo">
                </div>
                <div class="login-logo-shadow"></div>
            </div>
            <h2
                class="text-3xl font-bold text-gray-900 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Welcome To Talaria
            </h2>
            <p class="mt-2 text-sm text-gray-600">Please sign in to continue</p>
        </div>

        <!-- Flash Messages Section -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div
            class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800{% else %}bg-green-50 text-green-800{% endif %} text-sm">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    {% if category == 'error' %}
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {% else %}
                    <i class="fas fa-check-circle mr-2"></i>
                    {% endif %}
                </div>
                <div>{{ message }}</div>
            </div>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        <!-- Login Form -->
        <form class="space-y-6" action="{{ url_for('auth.login') }}" method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input id="username" name="username" type="email" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter your email address">
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input id="password" name="password" type="password" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter your password">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                            id="togglePassword">
                            <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                </div>
                <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-500">Forgot password?</a>
            </div>

            <button type="submit"
                class="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Sign in
            </button>
        </form>

        <!-- Login Information Box -->
        <div class="mt-6 bg-blue-50 p-4 rounded-lg text-sm text-blue-800">
            <p class="font-semibold mb-1"><i class="fas fa-info-circle mr-1"></i> Login Information:</p>
            <ul class="list-disc pl-5 space-y-1">
                <li><strong>Admin users:</strong> Use your admin email and master password</li>
                <li><strong>Colleagues:</strong> Use your email and the shared team password</li>
            </ul>
        </div>

        <!-- Contact Admin Link -->
        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
                Don't have an account?
                <a href="#" class="font-medium text-blue-600 hover:text-blue-500">Contact an administrator</a>
            </p>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');

        togglePassword.addEventListener('click', function () {
            // Toggle password visibility
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle icon between eye and eye-slash
            const eyeIcon = this.querySelector('i');
            eyeIcon.classList.toggle('fa-eye');
            eyeIcon.classList.toggle('fa-eye-slash');
        });
    });
</script>
{% endblock %}