# database/auth_schema.py
import os  # noqa F401
from database.db_config import get_db_connection


def setup_auth_tables():
    """
    Verify authentication-related tables exist and set up default permissions if needed.
    """
    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Check if tables exist
        cursor.execute(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'users'
            );
        """
        )
        users_table_exists = cursor.fetchone()[0]

        cursor.execute(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'permissions'
            );
        """
        )
        permissions_table_exists = cursor.fetchone()[0]

        if not users_table_exists:
            print("Warning: 'users' table does not exist. Please create it manually.")

        if not permissions_table_exists:
            print(
                "Warning: 'permissions' table does not exist. "
                "Please create it manually."
            )

        if not users_table_exists or not permissions_table_exists:
            return

        # Check if we need to insert default permissions
        cursor.execute("SELECT COUNT(*) FROM permissions")
        count = cursor.fetchone()[0]

        if count == 0:
            # Insert default permissions
            default_permissions = [
                ("admin", "view"),
                ("admin", "create"),
                ("admin", "modify"),
                ("admin", "delete"),
                ("admin", "sync"),
                ("colleague", "view"),
                ("colleague", "create"),
                ("colleague", "sync"),
            ]

            for role, permission in default_permissions:
                cursor.execute(
                    "INSERT INTO permissions (role, permission) VALUES (%s, %s)",
                    (role, permission),
                )

            print("Default permissions inserted")

        conn.commit()
        print("Authentication tables verification complete")

    except Exception as e:
        conn.rollback()
        print(f"Error verifying authentication tables: {e}")
    finally:
        cursor.close()
        conn.close()


if __name__ == "__main__":
    # This allows running this file directly to verify the tables
    setup_auth_tables()
