-- Chat training data table
CREATE TABLE IF NOT EXISTS chat_training_data (
    id SERIAL PRIMARY KEY,
    pattern TEXT NOT NULL,
    response TEXT NOT NULL,
    category VARCHAR(50) DEFAULT 'general',
    priority INTEGER DEFAULT 1,
    created_at TIM<PERSON><PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VA<PERSON><PERSON><PERSON>(100),
    active BOOLEAN DEFAULT TRUE
);

-- Create indices for faster lookups
CREATE INDEX IF NOT EXISTS idx_chat_pattern ON chat_training_data(pattern);
CREATE INDEX IF NOT EXISTS idx_chat_category ON chat_training_data(category);

-- Chat conversation history
CREATE TABLE IF NOT EXISTS chat_history (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100),
    user_message TEXT NOT NULL,
    bot_response TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    feedback INTEGER DEFAULT 0,
    session_id VARCHAR(100)
);

-- Chat feedback table
CREATE TABLE IF NOT EXISTS chat_feedback (
    id SERIAL PRIMARY KEY,
    chat_history_id INTEGER REFERENCES chat_history(id),
    rating INTEGER,
    comment TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert some initial training data
-- Original entries with enhancements
INSERT INTO chat_training_data (pattern, response, category, priority, created_by)
VALUES
('hello', 'Hello! How can I help you today? 😊', 'greeting', 10, 'system'),
('hi', 'Hi there! What can I assist you with? 👋', 'greeting', 10, 'system'),
('how are you', 'I''m doing great, thanks for asking! How can I assist you today? 😊', 'greeting', 5, 'system'),
('what can you do', 'I can provide information about inventory, help with shipment tracking, answer questions about the system, and much more! Just let me know what you need. 🚀', 'capabilities', 8, 'system'),
('thanks', 'You''re welcome! Is there anything else I can help you with? 👍', 'gratitude', 5, 'system'),
('thank you', 'You''re welcome! Is there anything else I can help you with? 👍', 'gratitude', 5, 'system'),
('goodbye', 'Goodbye! Feel free to chat again if you need anything else. 👋', 'farewell', 5, 'system'),
('bye', 'Bye for now! Come back anytime you need assistance. 👋', 'farewell', 5, 'system'),
('inventory', 'The inventory system tracks all wafers in our database. You can check current inventory levels from the Inventory tab in the main navigation. Need specific details? 📦', 'inventory', 7, 'system'),
('shipment', 'Shipment tracking is available through the Shipments Task section. You can create new shipments, track existing ones, and generate shipping labels. Need help with a specific shipment? 🚚', 'shipment', 7, 'system'),
('labels', 'You can generate wafer labels from the ''Generate Labels'' section. Select your lot, choose the wafers, and print the labels directly or download as PDF. 🏷️', 'labels', 7, 'system'),
('support', 'Need more help? You can submit a support ticket through the Support page, and our team will get back to you as soon as possible. 🆘', 'support', 6, 'system'),
('features', 'Talaria Dashboard offers inventory management, shipment tracking, label generation, and comprehensive reporting. Is there a specific feature you''d like to know more about? ✨', 'general', 6, 'system'),
('joke', 'Why don''t scientists trust atoms? Because they make up everything! 😄', 'fun', 3, 'system'),
('help', 'I can help you with information about inventory, shipments, and general system usage. Just ask me anything! 💬', 'help', 9, 'system');

-- Additional lively interactions
INSERT INTO chat_training_data (pattern, response, category, priority, created_by)
VALUES
-- More engaging greetings
('hey', 'Hey there! Ready to make your workflow smoother today? 🌟', 'greeting', 10, 'system'),
('good morning', 'Good morning! ☀️ The day is young and full of possibilities. How can I assist with your tasks today?', 'greeting', 10, 'system'),
('good afternoon', 'Good afternoon! ☀️ Hope your day is going well! What can I help you accomplish?', 'greeting', 10, 'system'),
('good evening', 'Good evening! 🌙 Still working? Your dedication is impressive! How can I make your evening more productive?', 'greeting', 10, 'system'),

-- More helpful responses
('search', 'Looking for something specific? You can use our powerful search feature by clicking the magnifying glass icon or pressing Ctrl+K. What are you trying to find? 🔍', 'help', 8, 'system'),
('dashboard', 'The dashboard gives you a bird''s eye view of your operations! 📊 You can see pending shipments, inventory alerts, and recent activities all in one place. Need help customizing it?', 'navigation', 7, 'system'),
('settings', 'You can personalize your experience in Settings! ⚙️ Change themes, notification preferences, and user details. What would you like to configure?', 'settings', 6, 'system'),

-- Error handling and troubleshooting
('error', 'Oops! Encountering an error? 🛠️ Please tell me what you were doing when it happened, and if possible, share the error code. I''ll help you get back on track!', 'troubleshooting', 9, 'system'),
('not working', 'Let''s troubleshoot together! 🔧 Could you describe what feature isn''t working as expected? Screenshots are super helpful if you can share them!', 'troubleshooting', 9, 'system'),
('slow', 'System running slow? 🐢 Try clearing your browser cache or using our lightweight mode in Settings > Performance. Still having issues? Let me know!', 'troubleshooting', 8, 'system'),

-- More specific feature help
('reports', 'Our Reports section is your data goldmine! 📈 Generate custom reports for inventory trends, shipment analytics, and more. You can schedule recurring reports too! What kind of insights are you looking for?', 'reports', 7, 'system'),
('notifications', 'Stay in the loop with custom notifications! 🔔 Set alerts for low inventory, shipment updates, or system messages. Configure them in Settings > Notifications. What would you like to be notified about?', 'notifications', 6, 'system'),
('import data', 'Importing data is a breeze! 📥 Go to Data Management > Import and upload your CSV or Excel file. Our system will guide you through mapping columns. Need a template to get started?', 'data', 8, 'system'),
('export data', 'Need to take your data elsewhere? 📤 Head to any data table, click the Export button, and choose your preferred format (CSV, Excel, or PDF). You can even schedule automated exports!', 'data', 8, 'system'),

-- More engaging conversation continuers
('i need help with', 'I''m here to help! 💪 Tell me more about what you''re working on, and I''ll provide the guidance you need.', 'help', 9, 'system'),
('how do i', 'Great question! I''d be happy to show you how. 🚀 Let me walk you through the process step by step.', 'help', 9, 'system'),
('not sure', 'No worries! Sometimes the system can be complex. 🧩 Let me break it down for you. What task are you trying to accomplish?', 'help', 8, 'system'),
('confused', 'Let''s clear up that confusion! 🔍 The system has many features, but we''ll focus on exactly what you need. Tell me what''s tripping you up.', 'help', 8, 'system'),

-- Fun interactions
('another joke', 'What did the database say to the developer? "You've got me in bits!" 🤣', 'fun', 3, 'system'),
('tell me a fact', 'Did you know? The average person spends 6 months of their lifetime waiting for red lights to turn green! 🚦 Now back to making your workflow more efficient!', 'fun', 3, 'system'),
('i''m bored', 'Bored? Let me entertain you with a productivity tip! 💡 Try the Pomodoro technique: 25 minutes of focused work, then a 5-minute break. Or shall I show you some hidden features in the system?', 'fun', 3, 'system'),
('surprise me', 'Did you know you can customize your dashboard with drag-and-drop widgets? 🎮 Many users don''t discover this until months in! Want me to show you how?', 'fun', 3, 'system'),

-- Encouragement and motivation
('this is hard', 'You''ve got this! 💪 Every expert was once a beginner. Let''s break this down into smaller steps. What part are you finding challenging?', 'motivation', 4, 'system'),
('i made a mistake', 'No worries! Mistakes are just opportunities to learn. 🌱 Many actions can be undone with the Undo button or by contacting support. What happened?', 'motivation', 4, 'system'),
('frustrated', 'I understand how frustrating technology can be sometimes. 😮‍💨 Let''s take a breath and tackle this together. What's causing your frustration?', 'motivation', 4, 'system'),

-- Industry-specific wafer interactions
('wafer specs', 'Our system handles wafers of all specifications! 🔄 You can filter by diameter (200mm, 300mm), material type, and doping level in the inventory section. Need help finding specific specs?', 'wafers', 7, 'system'),
('lot tracking', 'Lot tracking is comprehensive! 🔍 Follow wafers from incoming QC through processing and final shipment. Each lot history shows environmental data, handling records, and quality metrics. Looking for a specific lot?', 'wafers', 7, 'system'),
('yield analysis', 'Our yield analysis tools help identify patterns affecting your production quality! 📊 Access them in Reports > Yield Management. You can correlate environmental factors, process steps, and defect types. What metrics are you tracking?', 'wafers', 7, 'system'),
('wafer maps', 'Wafer maps give you visual insights into defect distribution and process uniformity! 🗺️ View them in the Quality section or generate them from raw test data. Would you like a tutorial on interpreting wafer maps?', 'wafers', 7, 'system');