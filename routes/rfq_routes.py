"""RFQ Routes for Talaria Dashboard.

This module handles the web routes for RFQ (Request for Quotation) email automation.
Provides endpoints for file upload, email sending, and status monitoring.

Developed for Ligentec SA - RFQ Email Automation Feature
"""

import logging
import os
from io import BytesIO

import pandas as pd
from flask import (
    Blueprint,
    current_app,
    flash,
    jsonify,
    redirect,
    render_template,
    request,
    send_file,
    url_for,
)
from flask_mail import Mail
from werkzeug.utils import secure_filename

from core.auth.auth import check_permission, login_required
from core.services.rfq_email_service import RFQEmailService

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
rfq_bp = Blueprint("rfq", __name__, url_prefix="/rfq")

# Initialize mail instance (will be set when app starts)
mail = None


def init_rfq_service():
    """Initialize RFQ service with mail instance."""
    global mail
    if mail is None:
        mail = Mail(current_app)
    return RFQEmailService(mail)


@rfq_bp.route("/")
@login_required
def rfq_automation():
    """Display the RFQ automation interface."""
    try:
        # Check if user has permission to use RFQ automation
        if not check_permission("modify"):
            flash("You do not have permission to access RFQ automation.", "error")
            return redirect(url_for("home"))

        return render_template("rfq_automation.html")

    except Exception as e:
        logger.error(f"Error loading RFQ automation page: {str(e)}")
        flash("Error loading RFQ automation page.", "error")
        return redirect(url_for("home"))


@rfq_bp.route("/upload", methods=["POST"])
@login_required
def upload_rfq_file():
    """Handle RFQ Excel file upload and validation."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to upload RFQ files.",
                    }
                ),
                403,
            )

        # Check if file was uploaded
        if "rfq_file" not in request.files:
            return jsonify({"success": False, "message": "No file uploaded."}), 400

        file = request.files["rfq_file"]

        # Initialize RFQ service
        rfq_service = init_rfq_service()

        # Validate file
        is_valid, error_message = rfq_service.validate_file(file)
        if not is_valid:
            return jsonify({"success": False, "message": error_message}), 400

        # Save file
        file_path = rfq_service.save_uploaded_file(file)

        # Parse Excel file
        rfq_records = rfq_service.parse_excel_file(file_path)

        if not rfq_records:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "No valid RFQ records found in the uploaded file.",
                    }
                ),
                400,
            )

        # Store file path in session for later use
        from flask import session

        session["rfq_file_path"] = file_path
        session["rfq_records_count"] = len(rfq_records)

        # Return preview data
        preview_data = []
        for record in rfq_records[:5]:  # Show first 5 records as preview
            preview_data.append(
                {
                    "order_id": record["order_id"],
                    "project_name": record["project_name"],
                    "links_count": len(record["links"]),
                    "priority": record["priority"],
                }
            )

        return jsonify(
            {
                "success": True,
                "message": f"File uploaded successfully. Found {len(rfq_records)} RFQ records.",
                "records_count": len(rfq_records),
                "preview_data": preview_data,
            }
        )

    except Exception as e:
        logger.error(f"Error uploading RFQ file: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error processing file: {str(e)}"}),
            500,
        )


@rfq_bp.route("/send-emails", methods=["POST"])
@login_required
def send_rfq_emails():
    """Send RFQ emails based on uploaded file."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send RFQ emails.",
                    }
                ),
                403,
            )

        # Get request data
        data = request.get_json()
        test_mode = data.get("test_mode", True)  # Default to test mode for safety

        # Get file path from session
        from flask import session

        file_path = session.get("rfq_file_path")

        if not file_path or not os.path.exists(file_path):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "No uploaded file found. Please upload a file first.",
                    }
                ),
                400,
            )

        # Initialize RFQ service
        rfq_service = init_rfq_service()

        # Parse file again to get fresh data
        rfq_records = rfq_service.parse_excel_file(file_path)

        if not rfq_records:
            return (
                jsonify({"success": False, "message": "No valid RFQ records found."}),
                400,
            )

        # Send emails
        results = rfq_service.send_rfq_emails(rfq_records, test_mode=test_mode)

        # Clean up session
        session.pop("rfq_file_path", None)
        session.pop("rfq_records_count", None)

        # Prepare response
        if results["errors"]:
            status_message = f"Partially completed: {results['emails_sent']} emails sent, {len(results['errors'])} errors"
            success = results["emails_sent"] > 0
        else:
            status_message = f"Successfully sent {results['emails_sent']} RFQ emails"
            success = True

        mode_text = "TEST MODE" if test_mode else "PRODUCTION MODE"

        return jsonify(
            {
                "success": success,
                "message": f"{status_message} ({mode_text})",
                "results": {
                    "total_records": results["total_records"],
                    "emails_sent": results["emails_sent"],
                    "errors_count": len(results["errors"]),
                    "errors": results["errors"][:5],  # Show first 5 errors
                    "test_mode": test_mode,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error sending RFQ emails: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error sending emails: {str(e)}"}),
            500,
        )


@rfq_bp.route("/test-email", methods=["POST"])
@login_required
def send_test_email():
    """Send a test RFQ email to verify configuration."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send test emails.",
                    }
                ),
                403,
            )

        # Initialize RFQ service
        rfq_service = init_rfq_service()

        # Create test record
        test_record = {
            "order_id": "TEST001",
            "project_name": "Test Project - Email Configuration",
            "links": [
                "https://example.com/test-link-1",
                "https://example.com/test-link-2",
            ],
            "priority": "Test",
            "notes": "This is a test email to verify RFQ automation configuration.",
        }

        # Send test email (always in test mode)
        rfq_service._send_single_rfq_email(test_record, test_mode=True)

        return jsonify(
            {
                "success": True,
                "message": "Test email sent <NAME_EMAIL>",
            }
        )

    except Exception as e:
        logger.error(f"Error sending test email: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error sending test email: {str(e)}"}
            ),
            500,
        )


@rfq_bp.route("/status")
@login_required
def rfq_status():
    """Get current RFQ automation status."""
    try:
        from flask import session

        status = {
            "file_uploaded": "rfq_file_path" in session,
            "records_count": session.get("rfq_records_count", 0),
            "upload_folder_exists": os.path.exists(
                os.path.join(current_app.root_path, "uploads/rfq")
            ),
        }

        return jsonify({"success": True, "status": status})

    except Exception as e:
        logger.error(f"Error getting RFQ status: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error getting status: {str(e)}"}),
            500,
        )


@rfq_bp.route("/clear-session", methods=["POST"])
@login_required
def clear_rfq_session():
    """Clear RFQ session data."""
    try:
        from flask import session

        # Remove RFQ-related session data
        session.pop("rfq_file_path", None)
        session.pop("rfq_records_count", None)

        return jsonify({"success": True, "message": "Session cleared successfully"})

    except Exception as e:
        logger.error(f"Error clearing RFQ session: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error clearing session: {str(e)}"}),
            500,
        )


@rfq_bp.route("/download-template")
@login_required
def download_template():
    """Download RFQ Excel template file."""
    try:
        # Create sample data for the template
        sample_data = [
            {
                "Order_ID": "RFQ001",
                "Project_Name": "Sample Project Alpha",
                "Links": "https://example.com/project-alpha-link1, https://example.com/project-alpha-link2",
                "Priority": "High",
                "Notes": "Urgent project requiring immediate quotation",
            },
            {
                "Order_ID": "RFQ002",
                "Project_Name": "Sample Project Beta",
                "Links": "https://example.com/project-beta-link1",
                "Priority": "Medium",
                "Notes": "Standard project timeline",
            },
            {
                "Order_ID": "RFQ003",
                "Project_Name": "Sample Project Gamma",
                "Links": "https://example.com/project-gamma-link1, https://example.com/project-gamma-link2",
                "Priority": "Low",
                "Notes": "Future planning project",
            },
        ]

        # Create DataFrame
        df = pd.DataFrame(sample_data)

        # Create Excel file in memory
        output = BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="RFQ_Data", index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            as_attachment=True,
            download_name="RFQ_Template.xlsx",
        )

    except Exception as e:
        logger.error(f"Error creating template: {str(e)}")
        flash("Error creating template file.", "error")
        return redirect(url_for("rfq.rfq_automation"))


# Error handlers for the blueprint
@rfq_bp.errorhandler(413)
def file_too_large(error):
    """Handle file too large error."""
    return (
        jsonify({"success": False, "message": "File too large. Maximum size is 10MB."}),
        413,
    )


@rfq_bp.errorhandler(500)
def internal_error(error):
    """Handle internal server errors."""
    logger.error(f"Internal error in RFQ routes: {str(error)}")
    return (
        jsonify(
            {
                "success": False,
                "message": "Internal server error. Please try again later.",
            }
        ),
        500,
    )
